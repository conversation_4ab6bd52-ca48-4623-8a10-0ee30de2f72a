# AI API Migration Summary

This document summarizes the migration from OpenAI to Google AI API and ElevenLabs for speech functionality.

## Changes Made

### 1. Dependencies Updated

**Added:**
- `@ai-sdk/google`: Official AI SDK provider for Google Gemini
- `@google/generative-ai`: Google's Generative AI SDK
- `elevenlabs`: ElevenLabs SDK for text-to-speech

**Updated package.json:**
```json
{
  "@ai-sdk/google": "^1.0.2",
  "@google/generative-ai": "^0.21.0", 
  "elevenlabs": "^0.15.0"
}
```

### 2. AI Configuration Changes

**File: `src/config/ai.ts`**
- Replaced OpenAI models with Google Gemini models:
  - `toolhandlerModel`: `google('gemini-1.5-pro-latest')`
  - `toolsetSelectionModel`: `google('gemini-1.5-flash-latest')`
- Updated voice options to use ElevenLabs voices:
  - <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>
- Removed `textToSpeechModel` (replaced with ElevenLabs)

### 3. Text-to-Speech Migration

**New Files:**
- `src/lib/elevenlabs.ts`: ElevenLabs helper functions
- `src/app/api/text-to-speech/route.ts`: New TTS endpoint using ElevenLabs

**Updated Files:**
- `src/app/api/get-required-toolsets/route.ts`: Now uses ElevenLabs for speech generation
- `src/hooks/chat/useAudioPlayer.ts`: Updated to handle MP3 format from ElevenLabs

**Features:**
- High-quality speech synthesis with ElevenLabs
- Configurable voice settings (stability, similarity boost, style)
- MP3 audio format support
- Free tier: 10,000 characters/month

### 4. Speech-to-Text (Unchanged)

**Kept OpenAI Whisper:**
- `src/app/api/speech-to-text/route.ts`: Still uses OpenAI Whisper
- Reason: Whisper is industry-leading for speech recognition
- No changes needed to existing functionality

### 5. Environment Variables

**New Required Variables:**
```env
# AI APIs
OPENAI_API_KEY=  # Still needed for Whisper speech-to-text
GOOGLE_AI_API_KEY=  # For Gemini text generation
ELEVENLABS_API_KEY=  # For text-to-speech
```

**Updated Documentation:**
- `README.md`: Updated environment variable section
- `INSTALL.md`: Added new API key descriptions

## Benefits of Migration

### Google AI (Gemini)
- ✅ Competitive performance with GPT models
- ✅ Good free tier (15 requests per minute)
- ✅ Potentially lower costs than OpenAI
- ✅ Official AI SDK support

### ElevenLabs TTS
- ✅ Superior voice quality compared to OpenAI TTS
- ✅ Free tier available (10,000 characters/month)
- ✅ Multiple high-quality voice options
- ✅ Advanced voice settings and customization

### OpenAI Whisper (Retained)
- ✅ Industry-leading speech recognition accuracy
- ✅ Existing implementation works well
- ✅ No migration needed

## API Usage Costs

### Google AI (Gemini)
- **Free Tier:** 15 requests per minute
- **Paid:** $0.00025 per 1K characters (input), $0.00075 per 1K characters (output)

### ElevenLabs
- **Free Tier:** 10,000 characters per month
- **Paid:** Starting at $5/month for 30,000 characters

### OpenAI (Whisper only)
- **Speech-to-Text:** $0.006 per minute

## Next Steps

1. **Set up API keys:**
   - Get Google AI API key from [Google AI Studio](https://aistudio.google.com/)
   - Get ElevenLabs API key from [ElevenLabs](https://elevenlabs.io/)
   - Keep existing OpenAI API key for Whisper

2. **Update environment variables:**
   - Add `GOOGLE_AI_API_KEY` to your `.env` file
   - Add `ELEVENLABS_API_KEY` to your `.env` file

3. **Test the migration:**
   - Test text generation with Gemini
   - Test text-to-speech with ElevenLabs
   - Verify speech-to-text still works with Whisper

## Rollback Plan

If issues arise, you can easily rollback by:
1. Reverting `src/config/ai.ts` to use OpenAI models
2. Updating the TTS endpoints to use OpenAI again
3. The speech-to-text functionality remains unchanged

All changes are modular and can be reverted independently.
