import { ElevenLabsApi, ElevenLabsApiError } from 'elevenlabs';
import { AIVoice } from '@/config/ai';

// Initialize ElevenLabs client
const elevenlabs = new ElevenLabsApi({
  apiKey: process.env.ELEVENLABS_API_KEY,
});

export interface GenerateSpeechOptions {
  text: string;
  voice?: AIVoice;
  model?: string;
  stability?: number;
  similarityBoost?: number;
  style?: number;
  useSpeakerBoost?: boolean;
}

export interface GenerateSpeechResult {
  audio: {
    base64: string;
  };
  format: string;
}

/**
 * Generate speech using ElevenLabs API
 */
export async function generateSpeechWithElevenLabs({
  text,
  voice = 'Rachel',
  model = 'eleven_multilingual_v2',
  stability = 0.5,
  similarityBoost = 0.8,
  style = 0.0,
  useSpeakerBoost = true,
}: GenerateSpeechOptions): Promise<GenerateSpeechResult> {
  if (!process.env.ELEVENLABS_API_KEY) {
    throw new Error('ElevenLabs API key not configured');
  }

  try {
    // Generate speech using ElevenLabs
    const audioStream = await elevenlabs.generate({
      voice,
      text,
      model_id: model,
      voice_settings: {
        stability,
        similarity_boost: similarityBoost,
        style,
        use_speaker_boost: useSpeakerBoost,
      },
    });

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of audioStream) {
      chunks.push(chunk);
    }
    const audioBuffer = Buffer.concat(chunks);

    // Convert to base64
    const audioBase64 = audioBuffer.toString('base64');

    return {
      audio: {
        base64: audioBase64,
      },
      format: 'mp3',
    };
  } catch (error) {
    if (error instanceof ElevenLabsApiError) {
      throw new Error(`ElevenLabs API error: ${error.message} (Status: ${error.statusCode})`);
    }
    throw new Error(`Failed to generate speech: ${String(error)}`);
  }
}

/**
 * Get available ElevenLabs voices
 */
export async function getAvailableVoices() {
  if (!process.env.ELEVENLABS_API_KEY) {
    throw new Error('ElevenLabs API key not configured');
  }

  try {
    const voices = await elevenlabs.voices.getAll();
    return voices.voices.map(voice => ({
      id: voice.voice_id,
      name: voice.name,
      category: voice.category,
      description: voice.description,
    }));
  } catch (error) {
    if (error instanceof ElevenLabsApiError) {
      throw new Error(`ElevenLabs API error: ${error.message} (Status: ${error.statusCode})`);
    }
    throw new Error(`Failed to get voices: ${String(error)}`);
  }
}
