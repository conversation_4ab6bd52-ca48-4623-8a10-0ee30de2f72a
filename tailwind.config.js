/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    fontFamily: {
      satoshi: ['<PERSON><PERSON>', 'sans-serif'],
    },
    extend: {
      colors: {
        baseBackground: 'rgb(var(--color-baseBackground) / <alpha-value>)',
        background: 'rgb(var(--color-background) / <alpha-value>)',
        backgroundContrast:
          'rgb(var(--color-backgroundContrast) / <alpha-value>)',
        sec_background: 'rgb(var(--color-sec_background) / <alpha-value>)',
        surface: 'rgb(var(--color-surface) / <alpha-value>)',
        textColor: 'rgb(var(--color-textColor) / <alpha-value>)',
        textColorContrast:
          'rgb(var(--color-textColorContrast) / <alpha-value>)',
        secText: 'rgb(var(--color-secText) / <alpha-value>)',
        border: 'rgb(var(--color-border) / <alpha-value>)',
        primary: 'rgb(var(--color-primary) / <alpha-value>)',
        primaryDark: 'rgb(var(--color-primaryDark) / <alpha-value>)',
        dashboardBackground:
          'rgb(var(--color-dashboardBackground) / <alpha-value>)',
      },
      spacing: {
        4.5: '1.125rem',
        5.5: '1.375rem',
        6.5: '1.625rem',
        7.5: '1.875rem',
        8.5: '2.125rem',
        9.5: '2.375rem',
        10.5: '2.625rem',
        11: '2.75rem',
        11.5: '2.875rem',
        12.5: '3.125rem',
        13: '3.25rem',
        13.5: '3.375rem',
        14: '3.5rem',
        14.5: '3.625rem',
        15: '3.75rem',
        15.5: '3.875rem',
        16: '4rem',
        16.5: '4.125rem',
        17: '4.25rem',
        17.5: '4.375rem',
        18: '4.5rem',
        18.5: '4.625rem',
        19: '4.75rem',
        19.5: '4.875rem',
        21: '5.25rem',
        21.5: '5.375rem',
        22: '5.5rem',
        22.5: '5.625rem',
        24.5: '6.125rem',
        25: '6.25rem',
        25.5: '6.375rem',
        26: '6.5rem',
        27: '6.75rem',
        27.5: '6.875rem',
        29: '7.25rem',
        29.5: '7.375rem',
        30: '7.5rem',
        31: '7.75rem',
        32.5: '8.125rem',
        34: '8.5rem',
        34.5: '8.625rem',
        35: '8.75rem',
        36.5: '9.125rem',
        37.5: '9.375rem',
        39: '9.75rem',
        39.5: '9.875rem',
        40: '10rem',
        42.5: '10.625rem',
        44: '11rem',
        45: '11.25rem',
        46: '11.5rem',
        47.5: '11.875rem',
        49: '12.25rem',
        50: '12.5rem',
        52: '13rem',
        52.5: '13.125rem',
        54: '13.5rem',
        54.5: '13.625rem',
        55: '13.75rem',
        55.5: '13.875rem',
        59: '14.75rem',
        60: '15rem',
        62.5: '15.625rem',
        65: '16.25rem',
        67: '16.75rem',
        67.5: '16.875rem',
        70: '17.5rem',
        72.5: '18.125rem',
        73: '18.25rem',
        75: '18.75rem',
        90: '22.5rem',
        94: '23.5rem',
        95: '23.75rem',
        100: '25rem',
        115: '28.75rem',
        125: '31.25rem',
        132.5: '33.125rem',
        150: '37.5rem',
        171.5: '42.875rem',
        180: '45rem',
        187.5: '46.875rem',
        203: '50.75rem',
        230: '57.5rem',
        242.5: '60.625rem',
      },
      maxWidth: {
        2.5: '0.625rem',
        3: '0.75rem',
        4: '1rem',
        11: '2.75rem',
        13: '3.25rem',
        14: '3.5rem',
        15: '3.75rem',
        22.5: '5.625rem',
        25: '6.25rem',
        30: '7.5rem',
        34: '8.5rem',
        35: '8.75rem',
        40: '10rem',
        42.5: '10.625rem',
        44: '11rem',
        45: '11.25rem',
        70: '17.5rem',
        90: '22.5rem',
        94: '23.5rem',
        125: '31.25rem',
        132.5: '33.125rem',
        142.5: '35.625rem',
        150: '37.5rem',
        180: '45rem',
        203: '50.75rem',
        230: '57.5rem',
        242.5: '60.625rem',
        270: '67.5rem',
        280: '70rem',
        292.5: '73.125rem',
      },
      maxHeight: {
        35: '8.75rem',
        70: '17.5rem',
        90: '22.5rem',
        550: '34.375rem',
        300: '18.75rem',
      },
      minWidth: {
        22.5: '5.625rem',
        42.5: '10.625rem',
        47.5: '11.875rem',
        75: '18.75rem',
      },
      keyframes: {
        'pulse-opacity': {
          '0%, 100%': { opacity: '0.6' },
          '50%': { opacity: '0.9' },
        },
        'pulse-opacity-slow': {
          '0%, 100%': { opacity: '0.4' },
          '50%': { opacity: '0.7' },
        },
        'pulse-opacity-slower': {
          '0%, 100%': { opacity: '0.2' },
          '50%': { opacity: '0.4' },
        },
        'pulse-radius': {
          '0%, 100%': { r: '42' },
          '50%': { r: '44' },
        },
        'pulse-radius-slow': {
          '0%, 100%': { r: '48' },
          '50%': { r: '52' },
        },
        'pulse-radius-slower': {
          '0%, 100%': { r: '56' },
          '50%': { r: '62' },
        },
        'pulse-slow': {
          '0%, 100%': { opacity: '0.4', transform: 'scale(1)' },
          '50%': { opacity: '0.7', transform: 'scale(1.03)' },
        },
        'pulse-slower': {
          '0%, 100%': { opacity: '0.2', transform: 'scale(1)' },
          '50%': { opacity: '0.5', transform: 'scale(1.05)' },
        },
        waveEffect: {
          '0%, 100%': { color: 'var(--base-color, rgba(255, 255, 255, 0.5))' },
          '50%': { color: 'var(--highlight-color, rgba(255, 255, 255, 1))' },
        },
        wave: {
          '0%, 100%': { transform: 'translateY(10%)' },
          '50%': { transform: 'translateY(5%)' },
        },
        rotating: {
          '0%, 100%': { transform: 'rotate(360deg)' },
          '50%': { transform: 'rotate(0deg)' },
        },
        tilt: {
          '0%, 50%, 100%': {
            transform: 'rotate(0deg)',
          },
          '25%': {
            transform: 'rotate(2deg)',
          },
          '75%': {
            transform: 'rotate(-2deg)',
          },
        },
      },
      animation: {
        'pulse-opacity': 'pulse-opacity 1.2s ease-in-out infinite',
        'pulse-opacity-slow': 'pulse-opacity-slow 1.5s ease-in-out infinite',
        'pulse-opacity-slower': 'pulse-opacity-slower 2s ease-in-out infinite',
        'pulse-radius': 'pulse-radius 1.2s ease-in-out infinite',
        'pulse-radius-slow': 'pulse-radius-slow 1.5s ease-in-out infinite',
        'pulse-radius-slower': 'pulse-radius-slower 2s ease-in-out infinite',
        'pulse-slow': 'pulse-slow 2s ease-in-out infinite',
        'pulse-slower': 'pulse-slower 3s ease-in-out infinite',
        'wave-effect':
          'waveEffect var(--wave-duration, 2s) infinite ease-in-out',
        wave: 'waveEffect 3s infinite ease-in-out',
        'ping-once': 'ping 5s cubic-bezier(0, 0, 0.2, 1)',
        rotating: 'rotating 30s linear infinite',
        'spin-1.5': 'spin 1.5s linear infinite',
        'spin-2': 'spin 2s linear infinite',
        'spin-3': 'spin 3s linear infinite',
        tilt: 'tilt 10s infinite linear',
      },
    },
  },
  plugins: ['tailwindcss-animate'],
};
