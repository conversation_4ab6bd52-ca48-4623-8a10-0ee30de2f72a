'use client';

import React from 'react';

interface SolaLogoProps {
  className?: string;
}

export default function SolaLogo({
  className,
}: SolaLogoProps): React.ReactElement {
  return (
    <svg
      className={className}
      viewBox="0 0 1280 1280"
      preserveAspectRatio="xMidYMid meet"
    >
      <g
        transform="translate(0.000000,1280.000000) scale(0.100000,-0.100000)"
        fill="currentColor"
        stroke="none"
      >
        <path
          d="M6280 10774 c-25 -2 -117 -8 -205 -14 -215 -14 -324 -27 -485 -57
-177 -32 -542 -123 -655 -163 -50 -17 -141 -49 -203 -71 -527 -184 -1141 -582
-1559 -1011 -380 -388 -692 -840 -916 -1325 -63 -134 -127 -313 -148 -413 -13
-60 6 -106 48 -116 49 -13 415 12 450 29 38 20 60 56 108 176 94 234 319 613
509 861 144 187 431 487 606 634 443 373 ************ 796 235 61 358 83 611
110 727 76 1464 -65 2146 -411 259 -132 475 -270 699 -447 148 -117 539 -515
656 -667 229 -297 436 -640 527 -874 38 -98 62 -145 88 -173 l36 -38 170 0
c237 0 333 20 333 71 0 25 -64 212 -113 329 -216 521 -560 1030 -987 1461
-233 235 -483 436 -755 608 -61 39 -411 231 -421 231 -1 0 -58 25 -126 56
-387 178 -935 338 -1308 383 -178 21 -536 42 -620 35z"
        />
        <path
          d="M10672 7028 c-48 -43 -140 -119 -242 -200 -187 -149 -443 -310 -687
-431 -359 -179 -688 -280 -1228 -378 -159 -28 -579 -47 -830 -37 -199 9 -436
35 -570 63 -38 9 -124 26 -190 40 -66 13 -172 38 -235 55 -63 16 -162 41 -220
55 -167 39 -668 205 -965 320 -148 57 -484 174 -560 195 -44 12 -134 39 -200
60 -110 36 -375 105 -570 150 -126 29 -238 48 -390 65 -77 8 -176 20 -220 27
-129 18 -743 22 -997 7 -261 -16 -386 -17 -521 -6 l-97 9 -21 -47 c-23 -52
-39 -219 -38 -395 1 -132 6 -144 54 -148 22 -2 72 1 110 7 39 6 174 15 300 21
127 5 324 15 438 21 360 20 976 -20 1247 -82 399 -90 759 -198 1280 -387 612
-221 623 -225 895 -307 425 -129 746 -202 1050 -241 204 -26 990 -27 1176 -2
672 93 1330 317 1899 646 137 79 408 264 458 312 44 43 48 56 42 160 -4 83
-68 460 -80 475 -19 23 -36 18 -88 -27z"
        />
        <path
          d="M2845 6024 c-85 -6 -372 -32 -485 -44 -151 -17 -382 -69 -426 -96
-38 -23 -44 -82 -24 -207 23 -138 52 -248 75 -283 16 -25 20 -26 77 -20 33 4
72 12 86 17 180 69 914 119 1307 90 562 -42 1056 -171 1935 -505 63 -24 176
-65 249 -91 74 -26 157 -56 184 -66 108 -43 513 -163 702 -209 291 -70 451
-101 665 -129 47 -6 121 -15 164 -21 437 -57 1003 -25 1568 89 336 69 517 122
808 241 188 77 241 85 253 40 11 -41 -96 -133 -318 -276 -234 -151 -370 -218
-675 -336 -162 -62 -510 -143 -724 -169 -133 -16 -846 -6 -1046 15 -192 20
-484 79 -725 146 -409 114 -320 83 -1165 397 -524 194 -745 268 -1016 337
-310 79 -383 88 -764 94 -414 6 -621 -10 -900 -71 -91 -20 -302 -84 -374 -113
-155 -63 -155 -118 -2 -437 178 -371 429 -749 734 -1103 162 -188 288 -306
567 -531 232 -187 674 -449 950 -563 277 -114 554 -204 750 -245 403 -84 512
-102 732 -120 379 -31 701 -11 1149 70 293 54 351 68 584 141 390 124 795 322
1165 570 402 269 829 688 1111 1091 315 450 549 934 682 1414 74 266 92 349
92 420 0 83 -1 84 -217 86 -213 3 -214 3 -438 -109 -381 -191 -816 -351 -1150
-424 -310 -67 -370 -77 -659 -110 -123 -14 -207 -16 -465 -11 -527 9 -800 45
-1330 174 -248 60 -570 165 -1021 334 -315 118 -411 152 -675 240 -618 206
-1024 277 -1620 283 -170 2 -337 2 -370 0z"
        />
      </g>
    </svg>
  );
}
